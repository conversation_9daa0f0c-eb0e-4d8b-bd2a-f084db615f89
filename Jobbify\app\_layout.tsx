// Import polyfills first to fix WebCrypto API warning
import 'react-native-get-random-values';

import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useFonts } from 'expo-font';
import { Slot, Stack, useSegments, useRouter } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect, useState, useCallback, useRef } from 'react';
import { AppProvider, useAppContext } from '../context/AppContext';
import { Text, View, ActivityIndicator, TouchableOpacity } from 'react-native';
import { supabase } from '../lib/supabase';
import { Session } from '@supabase/supabase-js';
import { LightTheme, DarkTheme } from '../constants/Theme';
import React from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getNextOnboardingStep } from '../services/onboardingService';

// Prevent the splash screen from auto-hiding before asset loading is complete
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded, error] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
    PoppinsRegular: require('../assets/fonts/Poppins-Regular.ttf'),
    PoppinsMedium: require('../assets/fonts/Poppins-Medium.ttf'),
    PoppinsBold: require('../assets/fonts/Poppins-Bold.ttf'),
    ...FontAwesome.font,
  });
  const [fontTimeout, setFontTimeout] = useState(false);

  useEffect(() => {
    if (error) throw error;
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  // Add a timeout for font loading
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!loaded) {
        setFontTimeout(true);
        console.warn('Font loading timeout!');
      }
    }, 3000);
    return () => clearTimeout(timer);
  }, [loaded]);

  if (!loaded && !fontTimeout) {
    return null;
  }

  if (fontTimeout && !loaded) {
    return (
      <Text style={{ color: 'red', marginTop: 100, fontSize: 20 }}>
        Font loading failed or is taking too long!
      </Text>
    );
  }

  return (
    <AppProvider>
      <RootLayoutNav />
    </AppProvider>
  );
}

function RootLayoutNav() {
  const { user, setUser, theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(false); // Changed to false to remove loading screen
  const sessionCheckedRef = React.useRef(false);

  // Track if we're currently updating user to prevent duplicate calls
  const isUpdatingUserRef = useRef(false);

  // Cache user data for faster startup
  const cacheUserData = async (userData: any) => {
    try {
      await AsyncStorage.setItem('cached_user_data', JSON.stringify(userData));
    } catch (error) {
      console.error('Error caching user data:', error);
    }
  };

  const loadCachedUserData = async () => {
    try {
      const cachedData = await AsyncStorage.getItem('cached_user_data');
      if (cachedData) {
        const userData = JSON.parse(cachedData);
        console.log('📦 Loading cached user data for faster startup:', userData);
        setUser(userData);
        return userData;
      }
    } catch (error) {
      console.error('Error loading cached user data:', error);
    }
    return null;
  };

  // Memoize the user setter to prevent unnecessary re-renders
  const handleUserUpdate = useCallback(async (sessionUser: any) => {
    if (!sessionUser) {
      setUser(null);
      await AsyncStorage.removeItem('cached_user_data');
      return;
    }

    // Prevent duplicate calls
    if (isUpdatingUserRef.current) {
      console.log('🔄 User update already in progress, skipping duplicate call');
      return;
    }

    isUpdatingUserRef.current = true;
    console.log('🔄 Starting handleUserUpdate for:', sessionUser.email);

    try {
      // First, try to load cached data for immediate display
      const cachedUser = await loadCachedUserData();

      // If no cached data, set basic user info immediately for better UX
      if (!cachedUser) {
        const basicUserData = {
          id: sessionUser.id,
          name: sessionUser.user_metadata?.name || sessionUser.email?.split('@')[0] || 'User',
          email: sessionUser.email || '',
          avatar: sessionUser.user_metadata?.avatar_url || '',
          skills: [],
          experience: [],
          userType: 'job_seeker' as 'job_seeker' | 'service_provider',
          onboardingCompleted: false,
        };

        console.log('👤 Setting basic user state immediately:', basicUserData);
        setUser(basicUserData);

      // No need to set loading state since it starts as false
      }

      // Then fetch basic profile data quickly in background
      console.log('📊 Fetching user profile...');

      // Use a simple, fast query for just the profile
      const { data: existingProfile, error: fetchError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', sessionUser.id)
        .single();

      let profileData = existingProfile;

      // If profile doesn't exist, create it with minimal data
      if (fetchError && fetchError.code === 'PGRST116') {
        console.log('➕ Creating new user profile for:', sessionUser.email);

        const { data: newProfile, error: createError } = await supabase
          .from('profiles')
          .insert({
            id: sessionUser.id,
            name: sessionUser.user_metadata?.name || sessionUser.email?.split('@')[0] || 'User',
            email: sessionUser.email || '',
            user_type: 'job_seeker' // Default to job seeker
          })
          .select('*')
          .single();

        if (createError) {
          console.error('❌ Error creating profile:', createError);
          // If it's a foreign key error, the auth session might be stale
          if (createError.code === '23503') {
            console.log('🔄 Foreign key error detected, clearing auth session...');
            await supabase.auth.signOut();
            return;
          }
        } else {
          profileData = newProfile;
          console.log('✅ Profile created successfully:', newProfile);
        }
      } else if (fetchError) {
        console.error('❌ Error fetching profile:', fetchError);
        // Use cached or basic user data if profile fetch fails
        return;
      } else {
        console.log('✅ Profile found with related data:', existingProfile);
      }

      // Update user state with profile data (keep it simple for fast startup)
      const fullUserData = {
        id: sessionUser.id,
        name: profileData?.name || sessionUser.user_metadata?.name || sessionUser.email?.split('@')[0] || 'User',
        email: sessionUser.email || '',
        avatar: sessionUser.user_metadata?.avatar_url || '',
        skills: [], // Load skills later to avoid startup delay
        experience: [], // Load experience later to avoid startup delay
        userType: profileData?.user_type || 'job_seeker',
        onboardingCompleted: profileData?.onboarding_completed || false,
      };

      console.log('👤 Updating user state with optimized profile data:', fullUserData);
      setUser(fullUserData);

      // Cache the updated user data
      await cacheUserData(fullUserData);

    } catch (error) {
      console.error('❌ Error in handleUserUpdate:', error);
      // Cached or basic user data was already set, so we don't need fallback here
    } finally {
      isUpdatingUserRef.current = false;
    }
  }, [setUser]);

  // Check for an existing session when the app loads
  useEffect(() => {
    // Prevent multiple session checks
    if (sessionCheckedRef.current) return;
    sessionCheckedRef.current = true;

    console.log('🔄 Starting initial session check...');

    supabase.auth.getSession().then(async ({ data: { session }, error }) => {
      console.log('📱 Initial session check result:', {
        hasSession: !!session,
        userEmail: session?.user?.email,
        error: error?.message
      });

      setSession(session);

      try {
        if (session?.user) {
          console.log('👤 User found, updating user state...');
          await handleUserUpdate(session.user);
        } else {
          console.log('❌ No user session found');
        }
      } catch (updateError) {
        console.error('❌ Error updating user:', updateError);
      } finally {
        console.log('✅ Session check complete');
      }
    }).catch((sessionError) => {
      console.error('❌ Error getting session:', sessionError);
    });

    // Listen for auth changes - but skip INITIAL_SESSION to avoid duplicate calls
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      // Skip INITIAL_SESSION event to prevent duplicate handleUserUpdate calls
      if (event === 'INITIAL_SESSION') {
        console.log('🔄 Skipping INITIAL_SESSION event to prevent duplicate calls');
        return;
      }

      console.log('🔄 Auth state change:', event, session?.user?.email);
      setSession(session);

      try {
        if (session?.user && event === 'SIGNED_IN') {
          console.log('👤 User signed in, updating user state...');
          await handleUserUpdate(session.user);
        } else if (!session && event === 'SIGNED_OUT') {
          console.log('👤 User signed out, clearing user state...');
          setUser(null);
          isUpdatingUserRef.current = false; // Reset the flag on sign out
        }
      } catch (updateError) {
        console.error('❌ Error in auth state change:', updateError);
      }
    });

    return () => subscription.unsubscribe();
  }, [handleUserUpdate]);

  // Route protection logic moved into its own effect to avoid calling hook inside hook
  const segments = useSegments();
  const router = useRouter();
  const processingRef = React.useRef(false);
  const prevSegmentsRef = React.useRef(segments);
  const prevUserRef = React.useRef(user);

  useEffect(() => {
    // Navigation logic runs immediately without waiting for loading state
    
    // Skip if we're already processing to prevent multiple redirects
    if (processingRef.current) {
      return;
    }
    
    // Skip if nothing has changed since last check
    if (
      segments === prevSegmentsRef.current &&
      user === prevUserRef.current
    ) {
      return;
    }
    
    // Update refs
    prevSegmentsRef.current = segments;
    prevUserRef.current = user;
    
    // Check if we're in the auth group
    const inAuthGroup = segments[0] === '(auth)';
    
    // Set processing flag to prevent multiple redirects
    processingRef.current = true;
    
    // IMPORTANT: Add a delay to ensure the root layout is fully mounted
    // This helps prevent the "Attempted to navigate before mounting the Root Layout" error
    const timer = setTimeout(() => {
      try {
        console.log('Attempting navigation based on auth state:', {
          user: !!user,
          inAuthGroup,
          currentSegment: segments[0],
          allSegments: segments,
          onboardingCompleted: user?.onboardingCompleted
        });

        // New flow: Welcome screen first, then auth when needed
        // If user is not signed in and not in welcome/onboarding flow, redirect to welcome
        if (!user && !inAuthGroup && segments[0] !== 'splash' && segments[0] !== '(onboarding)') {
          console.log('NEW FLOW: Redirecting unauthenticated user to welcome screen');
          router.replace('/(onboarding)/welcome');
        // If user is signed in and in auth group, redirect to main app
        } else if (user && inAuthGroup && segments[0] !== '(tabs)') {
          console.log('User authenticated, redirecting to main app');
          router.replace('/(tabs)');
        }
      } catch (navError) {
        console.error('Navigation error in root layout:', navError);
        // If navigation fails, we'll just stay on the current screen
      } finally {
        // Clear the processing flag
        processingRef.current = false;
      }
    }, 300); // Use a longer delay to ensure everything is ready
    
    // Clean up the timer if the component unmounts
    return () => clearTimeout(timer);
  }, [segments, user, router, isLoading]);

  // Removed loading screen - users go directly to their dedicated screen

  // Return a Slot instead of conditional Stack to fix the warning
  return <Slot />;
}
